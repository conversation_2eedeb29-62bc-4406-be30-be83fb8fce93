import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "./ui/button";
import { Textarea } from "./ui/textarea";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2, CheckCircle, Database, FileSpreadsheet, Copy } from "lucide-react";
import { processLlmValue, processDoublePipe } from "@/lib/utils";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

interface EnhancedFinalValue {
  Attribute?: string;
  finalValue: string;
  source: string;
  walmartValidation?: string;
  llmValidation?: string;
  walmartLatestValidation?: string; // ← ADDED
  walmartComment?: string;
  llmComment?: string;
  walmartLatestComments?: string;   // ← ADDED
  finalValidation?: string;
  finalComment?: string;
  finalVerdict?: string; // ✅ NEW
  rawData?: {
    walmart: string;
    llm: string;
    brand: string;
    competitors: Record<string, string>;
  };
}

interface FeedbackFormProps {
  responseText: string;
  id_str: string;
  finalValues: EnhancedFinalValue[];
  shortSummary: string;
  detailedSummary: string;
}

export default function FeedbackForm({
  responseText,
  id_str,
  finalValues,
  shortSummary,
  detailedSummary
}: FeedbackFormProps) {
  const [feedback, setFeedback] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [copied, setCopied] = useState(false);

  const [saveDetails, setSaveDetails] = useState<{
    columns: number;
    sheetName: string;
    spreadsheetUrl?: string;
    rowsAdded?: number;
    taskViewUrl?: string;
    bucketPath?: string;
  } | null>(null);
  const navigate = useNavigate();

  // ✅ Enhanced helper function to extract source URLs
  const extractSourceUrls = (parsedResponse: any) => {
    const urls: Record<string, string> = {};

    if (parsedResponse.competitors) {
      parsedResponse.competitors.forEach((comp: any) => {
        if (comp?.url) {
          try {
            const hostname = new URL(comp.url).hostname || "";
            const source = hostname.replace(/^www\./, "").split(".")[0];
            urls[source] = comp.url;
          } catch (e) {
            console.warn("Invalid competitor URL:", comp.url);
          }
        }
      });
    }

    return urls;
  };

  // ✅ TypeScript errors fixed with proper type guards
  const extractCompetitorSummary = (finalValues: EnhancedFinalValue[]) => {
    const allCompetitors = new Set<string>();
    const competitorData: Record<string, Record<string, string>> = {};
    const competitorUrls: Record<string, string> = {};

    // ✅ Added proper type checking for fv.Attribute
    finalValues.forEach(fv => {
      if (fv.rawData?.competitors && fv.Attribute) {
        Object.entries(fv.rawData.competitors).forEach(([competitor, value]) => {
          allCompetitors.add(competitor);

          if (!competitorData[competitor]) {
            competitorData[competitor] = {};
          }

          const attributeName = fv.Attribute!;
          competitorData[competitor][attributeName] = String(value || "-");
        });
      }
    });

    // ✅ Extract competitor URLs from parsed response
    try {
      const parsedResponse = JSON.parse(responseText);
      if (parsedResponse.competitors) {
        parsedResponse.competitors.forEach((comp: any) => {
          if (comp?.url) {
            try {
              const hostname = new URL(comp.url).hostname || "";
              const competitorName = hostname.replace(/^www\./, "").split(".")[0];
              competitorUrls[competitorName] = comp.url;
              allCompetitors.add(competitorName);

              if (comp.attributes && (!competitorData[competitorName] || Object.keys(competitorData[competitorName]).length === 0)) {
                if (!competitorData[competitorName]) {
                  competitorData[competitorName] = {};
                }
                Object.entries(comp.attributes).forEach(([attr, value]) => {
                  if (!competitorData[competitorName][attr]) {
                    competitorData[competitorName][attr] = String(value || "-");
                  }
                });
              }
            } catch (e) {
              console.warn("Invalid competitor URL:", comp.url);
            }
          }
        });
      }
    } catch (e) {
      console.warn("Error parsing response for competitor data:", e);
    }

    console.log("🔍 Competitor Summary Extracted:", {
      total: allCompetitors.size,
      names: Array.from(allCompetitors),
      data_keys: Object.keys(competitorData),
      sample_data: Object.entries(competitorData).slice(0, 2).reduce((acc, [comp, attrs]) => {
        acc[comp] = Object.keys(attrs);
        return acc;
      }, {} as Record<string, string[]>)
    });

    return {
      total_competitors: allCompetitors.size,
      competitor_names: Array.from(allCompetitors),
      competitor_data: competitorData,
      competitor_urls: competitorUrls
    };
  };

  // ✅ TypeScript errors fixed with proper type guards
  const extractProductAttributes = (finalValues: EnhancedFinalValue[], parsedResponse: any) => {
    const attributes: Record<string, any> = {};

    finalValues.forEach(fv => {
      if (fv.Attribute) {
        const attributeName = fv.Attribute;

        const cleanCompetitorData: Record<string, string> = {};

        if (fv.rawData?.competitors) {
          Object.entries(fv.rawData.competitors).forEach(([key, value]) => {
            cleanCompetitorData[key] = String(value || "-");
          });
        }

        if (Object.keys(cleanCompetitorData).length === 0 && parsedResponse.competitors) {
          parsedResponse.competitors.forEach((comp: any) => {
            if (comp?.url && comp?.attributes?.[attributeName]) {
              try {
                const hostname = new URL(comp.url).hostname || "";
                const competitorName = hostname.replace(/^www\./, "").split(".")[0];
                if (comp.attributes[attributeName]) {
                  cleanCompetitorData[competitorName] = String(comp.attributes[attributeName] || "-");
                }
              } catch (e) {
                console.warn("Error extracting fallback competitor data:", e);
              }
            }
          });
        }

        if (Object.keys(cleanCompetitorData).length > 0) {
          console.log(`🔍 Attribute ${attributeName} competitor data:`, cleanCompetitorData);
        }

        attributes[attributeName] = {
          walmart_latest_value: parsedResponse.walmart_llm?.attributes?.[attributeName] || "-",
          llm_suggested_value: processLlmValue(fv.rawData?.llm || parsedResponse.llm_suggested?.[attributeName]?.value || "-", attributeName),
          llm_suggested_url: parsedResponse.llm_suggested?.[attributeName]?.source_url || "-",
          values_from_sources: {
            walmart: parsedResponse.walmart?.attributes?.[attributeName] || fv.rawData?.walmart || "-",
            llm: processLlmValue(fv.rawData?.llm || "-", attributeName),
            brand: fv.rawData?.brand || "-",
            ...cleanCompetitorData
          },
          competitor_values: cleanCompetitorData,
          validation_data: {
            walmart_validation: fv.walmartValidation || "",
            walmart_latest_validation: fv.walmartLatestValidation || "", // ← ADDED
            llm_validation: fv.llmValidation || "",
            final_validation: fv.finalValidation || "",
            final_verdict: fv.finalVerdict || ""
          },
          comment_data: {
            walmart_comment: fv.walmartComment || "",
            walmart_latest_comment: fv.walmartLatestComments || "", // ← ADDED
            llm_comment: fv.llmComment || "",
            final_comment: fv.finalComment || "No Walmart or LLM data selected for this attribute"
          },
          selection_data: {
            final_value: fv.finalValue || "-",
            source: fv.source || "-"
          }
        };
      }
    });

    const attributesWithCompetitors = Object.entries(attributes).filter(([, data]) =>
      data.competitor_values && Object.keys(data.competitor_values).length > 0
    );

    console.log("🔍 Product Attributes Summary:", {
      total_attributes: Object.keys(attributes).length,
      attributes_with_competitor_data: attributesWithCompetitors.length,
      sample: attributesWithCompetitors.slice(0, 2).map(([attr, data]) => ({
        attribute: attr,
        competitors: Object.keys(data.competitor_values)
      }))
    });

    return attributes;
  };

  // ✅ Enhanced createTaskData function with proper type safety
  const createTaskData = (wideFormatData: Record<string, string>) => {
    const parsedResponse = JSON.parse(responseText);
    const competitorSummary = extractCompetitorSummary(finalValues);
    const productAttributes = extractProductAttributes(finalValues, parsedResponse);

    const competitorDataStats = {
      total_competitors: competitorSummary.total_competitors,
      competitor_names: competitorSummary.competitor_names,
      competitor_data_keys: Object.keys(competitorSummary.competitor_data),
      attributes_with_competitor_data: Object.values(productAttributes).filter(attr =>
        attr.competitor_values && Object.keys(attr.competitor_values).length > 0
      ).length,
      sample_competitor_data: Object.entries(competitorSummary.competitor_data).slice(0, 2)
    };

    const taskData = {
      // ✅ FIXED: Use correct field name
      user: wideFormatData["user"] || "",
      timestamp: new Date().toISOString(),
      submission_id: id_str,
      walmart_url: parsedResponse.walmart?.url || "",
      walmart_latest_url: parsedResponse.walmart_llm?.url || "-",
      product_type: parsedResponse.product_type || "",
      category: parsedResponse.category || "",
      gtin: parsedResponse.gtin || "-",
      product_name: parsedResponse.walmart?.product_name || parsedResponse.walmart_llm?.product_name || "",
      item_id: wideFormatData["Item ID"] || id_str,
      final_llm_link: "-",
      source_urls: extractSourceUrls(parsedResponse),
      product_attributes: productAttributes,
      competitor_summary: competitorSummary,
      // ✅ ADD THIS SECTION:
      ai_insights: {
        short_summary: shortSummary || "",
        detailed_summary: detailedSummary || "",
        generated_at: new Date().toISOString(),
        generation_source: "user_feedback_form"
      },

      raw_final_values: finalValues.map(fv => ({
        attribute: fv.Attribute || "Unknown",
        final_value: fv.finalValue,
        source: fv.source,
        raw_data: fv.rawData,
        validations: {
          walmart: fv.walmartValidation,
          walmart_latest: fv.walmartLatestValidation, // ← ADDED
          llm: fv.llmValidation,
          final: fv.finalValidation,
          verdict: fv.finalVerdict
        },
        comments: {
          walmart: fv.walmartComment,
          walmart_latest: fv.walmartLatestComments, // ← ADDED
          llm: fv.llmComment,
          final: fv.finalComment
        }
      })),
      data_summary: {
        total_attributes: finalValues.length,
        total_competitors: competitorSummary.total_competitors,
        competitor_names: competitorSummary.competitor_names,
        has_competitor_data: competitorDataStats.attributes_with_competitor_data > 0,
        competitor_data_stats: competitorDataStats,
        validation_counts: {
          walmart_yes: finalValues.filter(fv => fv.walmartValidation?.toLowerCase() === 'yes').length,
          walmart_no: finalValues.filter(fv => fv.walmartValidation?.toLowerCase() === 'no').length,
          walmart_latest_yes: finalValues.filter(fv => fv.walmartLatestValidation?.toLowerCase() === 'yes').length, // ← ADDED
          walmart_latest_no: finalValues.filter(fv => fv.walmartLatestValidation?.toLowerCase() === 'no').length,   // ← ADDED
          llm_yes: finalValues.filter(fv => fv.llmValidation?.toLowerCase() === 'yes').length,
          llm_no: finalValues.filter(fv => fv.llmValidation?.toLowerCase() === 'no').length,
          final_verdicts: finalValues.filter(fv => fv.finalVerdict && fv.finalVerdict.trim()).length
        }
      }
    };

    console.log("🔍 COMPLETE TASK DATA CREATED:", {
      competitor_stats: competitorDataStats,
      has_competitor_data: taskData.data_summary.has_competitor_data,
      competitor_summary_data: Object.keys(taskData.competitor_summary.competitor_data).length > 0,
      sample_attributes_with_competitors: Object.entries(taskData.product_attributes)
        .filter(([, data]) => Object.keys(data.competitor_values || {}).length > 0)
        .slice(0, 3)
        .map(([attr, data]) => ({
          attribute: attr,
          competitors: Object.keys(data.competitor_values || {}),
          sample_values: Object.entries(data.competitor_values || {}).slice(0, 2)
        }))
    });

    return taskData;
  };

  // ✅ Copy task link function
  const copyTaskLink = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  // ✅ COMPLETELY FIXED transformToWideFormat function with all missing fields
  const transformToWideFormat = () => {
    try {
      const parsedResponse = JSON.parse(responseText);
      const wideData: Record<string, string> = {};

      // Helper function to clean NaN values (same as in ResponseArea)
      const cleanNaNValue = (value: any): string => {
        if (value === null || value === undefined || value === "" ||
          String(value).toLowerCase() === "nan" ||
          String(value).toLowerCase() === "null") {
          return "-";
        }
        return String(value);
      };



      console.log("🔄 Starting wide format transformation for Google Sheets");

let submittedBy = "";
      try {
        const storedUser = localStorage.getItem("user");
        if (storedUser) {
          const u = JSON.parse(storedUser);
          submittedBy = u?.username || u?.name || u?.email || "";
        }
      } catch (e) {
        // ignore
      }
      if (submittedBy) wideData["user"] = submittedBy;

      // ✅ Basic info columns (EXACT MATCH to CSV export)
      wideData['Timestamp'] = new Date().toISOString();
      wideData['Submission ID'] = id_str || '';
      wideData["Walmart URL"] = cleanNaNValue(parsedResponse.walmart?.url);
      wideData["Walmart_Latest URL"] = cleanNaNValue(parsedResponse.walmart_llm?.url);
      wideData["Product Type"] = cleanNaNValue(parsedResponse.product_type);
      wideData["Category"] = cleanNaNValue(parsedResponse.category);

      // Extract Product ID from URL
      const walmartUrl = cleanNaNValue(parsedResponse.walmart?.url);
      if (walmartUrl && walmartUrl !== "-" && walmartUrl.includes("walmart.com/ip/")) {
        const productId = walmartUrl.split("/ip/")[1]?.split("/")[0] || "";
        wideData["Product ID"] = cleanNaNValue(parsedResponse.walmart?.product_id || productId);
        wideData["Item ID"] = cleanNaNValue(productId);
      } else {
        wideData["Product ID"] = "-";
        wideData["Item ID"] = "-";
      }

      wideData["Product Name"] = cleanNaNValue(parsedResponse.walmart?.product_name || parsedResponse.walmart_llm?.product_name || "");
      wideData["Final LLM Link"] = "-"; // Will be filled if available

      // ✅ Get all competitor columns dynamically
      const allCompetitors = new Set<string>();

      // Extract competitors from response data
      if (parsedResponse.competitors) {
        parsedResponse.competitors.forEach((comp: any, index: number) => {
          if (comp && comp.url) {
            try {
              const hostname = new URL(comp.url).hostname || "";
              const competitorName = hostname.replace(/^www\./, "").split(".")[0] || `competitor${index + 1}`;
              allCompetitors.add(competitorName);
            } catch (e) {
              allCompetitors.add(`competitor${index + 1}`);
            }
          }
        });
      }

      // Also check finalValues for any competitor data
      finalValues.forEach(fv => {
        if (fv.rawData?.competitors) {
          Object.keys(fv.rawData.competitors).forEach(comp => {
            allCompetitors.add(comp);
          });
        }
      });

      console.log(`Found ${allCompetitors.size} competitors:`, Array.from(allCompetitors));

      // ✅ Add competitor source URLs to basic info
      allCompetitors.forEach(competitor => {
        const competitorUrl = parsedResponse.competitors?.find((c: any) => {
          if (!c?.url) return false;
          try {
            const hostname = new URL(c.url).hostname || "";
            const name = hostname.replace(/^www\./, "").split(".")[0];
            return name === competitor;
          } catch {
            return false;
          }
        })?.url || "";
        wideData[`${competitor} Source URL`] = cleanNaNValue(competitorUrl);
      });

      // Get all unique attributes from finalValues
      const allAttributes = new Set<string>();
      finalValues.forEach(fv => {
        if (fv.Attribute) {
          allAttributes.add(fv.Attribute);
        }
      });

      console.log(`Processing ${allAttributes.size} attributes for Google Sheets wide format`);

      // ✅ Process each attribute with ALL COLUMNS from CSV export
      Array.from(allAttributes).forEach(attribute => {
        const fvIndex = finalValues.findIndex(fv => fv.Attribute === attribute);
        const fv = fvIndex !== -1 ? finalValues[fvIndex] : null;

        // ✅ Original data columns
        wideData[`${attribute} Initial prefilled values`] = cleanNaNValue(parsedResponse.walmart?.attributes?.[attribute]);
        wideData[`${attribute} Walmart_Latest values`] = cleanNaNValue(parsedResponse.walmart_llm?.attributes?.[attribute]);

        // ✅ LLM data
        const llmData = parsedResponse.llm_suggested?.[attribute];
        wideData[`${attribute} LLM Suggested value`] = processLlmValue(llmData?.value || llmData, attribute);
        wideData[`${attribute} LLM_suggested_url`] = cleanNaNValue(llmData?.source_url || "-");

        // ✅ Current values
        wideData[`${attribute}_Walmart_value`] = processDoublePipe(cleanNaNValue(parsedResponse.walmart?.attributes?.[attribute] || fv?.rawData?.walmart), attribute);
        wideData[`${attribute}_Brand_value`] = cleanNaNValue(fv?.rawData?.brand || parsedResponse.brand?.attributes?.[attribute]);
        wideData[`${attribute}_WalmartLatest_value`] = cleanNaNValue(parsedResponse.walmart_llm?.attributes?.[attribute]);
        wideData[`${attribute}_LLM_value`] = cleanNaNValue(processLlmValue(fv?.rawData?.llm || llmData?.value || llmData, attribute));

        // ✅ Add ALL competitor values
        allCompetitors.forEach(competitor => {
          let competitorValue = "-";

          // Try to get from finalValues rawData
          if (fv?.rawData?.competitors?.[competitor]) {
            competitorValue = fv.rawData.competitors[competitor];
          } else {
            // Try to get from parsedResponse competitors
            const compData = parsedResponse.competitors?.find((c: any) => {
              if (!c?.url) return false;
              try {
                const hostname = new URL(c.url).hostname || "";
                const name = hostname.replace(/^www\./, "").split(".")[0];
                return name === competitor;
              } catch {
                return false;
              }
            });
            if (compData?.attributes?.[attribute]) {
              competitorValue = compData.attributes[attribute];
            }
          }

          wideData[`${attribute}_${competitor}_value`] = cleanNaNValue(competitorValue);
        });

        // ✅ Validation data
        wideData[`${attribute}_Walmart_Validated`] = cleanNaNValue(fv?.walmartValidation || "");
        wideData[`${attribute}_LLM_Validate`] = cleanNaNValue(fv?.llmValidation || "");
        wideData[`${attribute}_LLM_Validation`] = cleanNaNValue(fv?.llmValidation || "");
        wideData[`${attribute}_WalmartLatest_Validation`] = cleanNaNValue(fv?.walmartLatestValidation || ""); // ← ADDED
        wideData[`${attribute}_Final_Validation`] = cleanNaNValue(fv?.finalValidation || "");

        // ✅ Comments data
        wideData[`${attribute}_Walmart_Comment`] = cleanNaNValue(fv?.walmartComment || "");
        wideData[`${attribute}_WalmartLatest_Comment`] = cleanNaNValue(fv?.walmartLatestComments || ""); // ← ADDED
        wideData[`${attribute}_LLM_Comment`] = cleanNaNValue(fv?.llmComment || "");

        // ✅ FIXED: Include WalmartLatest in final comment
        const allComments = [
          fv?.walmartComment,
          fv?.walmartLatestComments,
          fv?.llmComment
        ].filter(c => c && c.trim() && c !== "-").map(c => c!.replace(/-/g, "").replace(/\b\w/g, l => l.toUpperCase()));

        wideData[`${attribute}_Final_Comment`] = allComments.join(" | ") || "-";

        // ✅ Final Verdict
        wideData[`${attribute}_Final_Verdict`] = cleanNaNValue(fv?.finalVerdict || "");

        // ✅ Final combined values
        wideData[`${attribute}_Final_Choice`] = cleanNaNValue(fv?.finalValue || "");
        wideData[`${attribute}_Final_Source`] = cleanNaNValue(fv?.source || "");
        wideData[`${attribute}_Final_Verdict_Combined`] = cleanNaNValue(fv?.finalVerdict || "");

        // ✅ Selection status
        const selectedSources = [];
        if (fv?.finalValue && fv.finalValue !== "-") {
          if (fv.source?.includes("Walmart")) selectedSources.push("Walmart");
          if (fv.source?.includes("LLM")) selectedSources.push("LLM");
          if (fv.source?.includes("Brand")) selectedSources.push("Brand");
          allCompetitors.forEach(comp => {
            if (fv.source?.toLowerCase().includes(comp.toLowerCase())) {
              selectedSources.push(comp);
            }
          });
        }
        wideData[`${attribute}_Selected_Sources`] = selectedSources.join(", ") || "-";
      });

      // ✅ Summary statistics
      const validationCounts = {
        walmartYes: 0,
        walmartNo: 0,
        walmartLatestYes: 0, // ← ADDED
        walmartLatestNo: 0,  // ← ADDED
        llmYes: 0,
        llmNo: 0,
        finalVerdicts: 0
      };

      finalValues.forEach(fv => {
        if (fv.walmartValidation?.toLowerCase() === 'yes') validationCounts.walmartYes++;
        if (fv.walmartValidation?.toLowerCase() === 'no') validationCounts.walmartNo++;
        if (fv.walmartLatestValidation?.toLowerCase() === 'yes') validationCounts.walmartLatestYes++; // ← ADDED
        if (fv.walmartLatestValidation?.toLowerCase() === 'no') validationCounts.walmartLatestNo++;   // ← ADDED
        if (fv.llmValidation?.toLowerCase() === 'yes') validationCounts.llmYes++;
        if (fv.llmValidation?.toLowerCase() === 'no') validationCounts.llmNo++;
        if (fv.finalVerdict && fv.finalVerdict.trim()) validationCounts.finalVerdicts++;
      });

      wideData['Total_Attributes'] = String(allAttributes.size);
      wideData['Total_Selections'] = "0"; // Would need to be passed from parent
      wideData['Total_Competitors'] = String(allCompetitors.size);
      wideData['Competitor_Names'] = Array.from(allCompetitors).join(", ") || "-";
      wideData['Walmart_Yes_Count'] = String(validationCounts.walmartYes);
      wideData['Walmart_No_Count'] = String(validationCounts.walmartNo);
      wideData['WalmartLatest_Yes_Count'] = String(validationCounts.walmartLatestYes); // ← ADDED
      wideData['WalmartLatest_No_Count'] = String(validationCounts.walmartLatestNo);   // ← ADDED
      wideData['LLM_Yes_Count'] = String(validationCounts.llmYes);
      wideData['LLM_No_Count'] = String(validationCounts.llmNo);
      wideData['Final_Verdicts_Count'] = String(validationCounts.finalVerdicts);

      console.log(`✅ Wide format data prepared with ${Object.keys(wideData).length} fields for Google Sheets`);
      console.log(`✅ Competitors included: ${Array.from(allCompetitors).join(', ')}`);
      console.log(`✅ WalmartLatest validation counts: Yes=${validationCounts.walmartLatestYes}, No=${validationCounts.walmartLatestNo}`); // ← ADDED

      return wideData;
    } catch (error) {
      console.error("❌ Error transforming to wide format for Google Sheets:", error);
      return {};
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setErrorMsg("");
    setSaveDetails(null);

    const wideFormatData = transformToWideFormat();
    const taskData = createTaskData(wideFormatData);

    console.log("🔍 PRE-SUBMISSION COMPREHENSIVE COMPETITOR DATA CHECK:");
    console.log("1. Task Data Competitor Summary:", taskData.competitor_summary);
    console.log("2. Competitor Data Keys:", Object.keys(taskData.competitor_summary.competitor_data || {}));
    console.log("3. Sample Competitor Data:", Object.entries(taskData.competitor_summary.competitor_data || {}).slice(0, 2));
    console.log("4. Attributes with Competitor Values:",
      Object.entries(taskData.product_attributes).filter(([, data]) =>
        data.competitor_values && Object.keys(data.competitor_values).length > 0
      ).length
    );
    console.log("5. Sample Product Attributes with Competitors:",
      Object.entries(taskData.product_attributes).filter(([, data]) =>
        data.competitor_values && Object.keys(data.competitor_values).length > 0
      ).slice(0, 3).map(([attr, data]) => ({
        attribute: attr,
        competitor_values: data.competitor_values
      }))
    );

    const feedbackPayload = {
      id_str,
      feedback,
      finalValues,
      submitted_by: wideFormatData["user"] || "", // ✅ FIXED: Use correct field
      shortSummary,
      detailedSummary,
      rawJson: responseText,
      wideFormatData,
      saveToGoogleSheets: true,
      saveToBucket: true,
      task_data: taskData,
      sheetMetadata: {
        // ✅ FIXED: Use correct field name
        productId: wideFormatData["Product ID"] || id_str,
        timestamp: new Date().toISOString(),
        totalColumns: Object.keys(wideFormatData).length,
        totalAttributes: finalValues.length,
        totalCompetitors: taskData.competitor_summary.total_competitors,
        competitorNames: taskData.competitor_summary.competitor_names.join(", "),
        competitorDataPresent: Object.keys(taskData.competitor_summary.competitor_data || {}).length > 0,
        attributesWithCompetitorData: Object.values(taskData.product_attributes).filter(attr =>
          attr.competitor_values && Object.keys(attr.competitor_values).length > 0
        ).length,
        // ✅ ADDED: WalmartLatest validation statistics
        walmartLatestValidationCounts: {
          yes: finalValues.filter(fv => fv.walmartLatestValidation?.toLowerCase() === 'yes').length,
          no: finalValues.filter(fv => fv.walmartLatestValidation?.toLowerCase() === 'no').length
        }
      }
    };

    try {
      console.log("📤 Sending COMPLETE payload to backend with verified competitor data and WalmartLatest fields");
      console.log("📊 Final Payload Competitor Data Preview:", {
        competitor_summary: {
          total: taskData.competitor_summary.total_competitors,
          names: taskData.competitor_summary.competitor_names,
          has_data: Object.keys(taskData.competitor_summary.competitor_data || {}).length > 0
        },
        sample_attributes: Object.keys(taskData.product_attributes).slice(0, 3),
        metadata: feedbackPayload.sheetMetadata
      });

      const fbRes = await fetch(`${API_BASE_URL}/query/feedback`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(feedbackPayload),
      });

      if (!fbRes.ok) {
        const errorData = await fbRes.text();
        throw new Error(`Feedback failed: ${errorData}`);
      }

      const feedbackResult = await fbRes.json();
      console.log("📊 Google Sheets save result:", feedbackResult);

      // ✅ Enhanced save details with Google Sheets specific info
      setSaveDetails({
        columns: feedbackResult.wide_format_columns || Object.keys(wideFormatData).length,
        sheetName: feedbackResult.saved_to_sheet || "Product Analysis Data",
        spreadsheetUrl: feedbackResult.spreadsheet_url,
        rowsAdded: feedbackResult.rows_added || 1,
        taskViewUrl: `/task/${taskData.item_id}/view`,
        bucketPath: feedbackResult.bucket_path
      });
      setSubmitted(true);

      // ✅ NEW: Update status with task URL
      const taskUrl = `${window.location.origin}/task/${taskData.item_id}/view`;
      await fetch(`${API_BASE_URL}/assign/update-status-with-task-url`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id_str,
          new_status: "Completed",
          task_url: taskUrl
        }),
      });

      setTimeout(() => navigate("/user"), 3000);

    } catch (err: any) {
      console.error("❌ Error saving to Google Sheets and Bucket:", err);
      setErrorMsg(err.message || "Failed to submit feedback and save data. Try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mt-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 p-5 space-y-4 shadow-sm">
      <h3 className="font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
        <FileSpreadsheet className="h-5 w-5 text-green-600" />
        Submit Feedback & Save to Google Sheets & Bucket
      </h3>

      {/* Textarea */}
      <Textarea
        placeholder="Share your feedback about the data quality, accuracy, and any issues you encountered..."
        value={feedback}
        onChange={(e) => setFeedback(e.target.value)}
        disabled={submitted || loading}
        className="resize-none"
        rows={4}
      />

      {/* Submit Button */}
      {!submitted && (
        <Button
          onClick={handleSubmit}
          disabled={!feedback.trim() || loading}
          className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Saving to Sheets & Bucket...
            </>
          ) : (
            <>
              <FileSpreadsheet className="h-4 w-4" />
              Submit & Save to Sheets & Bucket
            </>
          )}
        </Button>
      )}

      {/* Animated Messages */}
      <AnimatePresence mode="wait">
        {submitted && saveDetails && (
          <motion.div
            key="success"
            initial={{ opacity: 0, y: 8 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -8 }}
            className="space-y-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800"
          >
            <div className="flex items-center gap-2 text-green-700 dark:text-green-400 font-medium">
              <CheckCircle className="h-5 w-5" />
              ✅ Successfully saved to Google Sheets & Bucket!
            </div>

            {/* Task view link */}
            {saveDetails.taskViewUrl && (
              <div className="flex items-center gap-2 mb-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const url = `${window.location.origin}${saveDetails.taskViewUrl}`;
                    copyTaskLink(url);
                  }}
                  className="flex items-center gap-1"
                >
                  <Copy className="h-3 w-3" />
                  {copied ? 'Copied!' : 'Copy Task Link'}
                </Button>
                <a
                  href={saveDetails.taskViewUrl}
                  className="text-blue-600 hover:underline text-sm font-medium"
                >
                  🔗 View Task
                </a>
              </div>
            )}

            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
                <Database className="h-4 w-4" />
                📊 {saveDetails.columns} columns saved to "{saveDetails.sheetName}"
              </div>

              {saveDetails.rowsAdded && (
                <div className="flex items-center gap-2 text-purple-600 dark:text-purple-400">
                  <FileSpreadsheet className="h-4 w-4" />
                  📄 {saveDetails.rowsAdded} row(s) added to spreadsheet
                </div>
              )}

              {saveDetails.bucketPath && (
                <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                  <Database className="h-4 w-4" />
                  💾 Task saved to: wal_test_bucket/{saveDetails.bucketPath}
                </div>
              )}

              {saveDetails.spreadsheetUrl && (
                <div className="mt-2">
                  <a
                    href={saveDetails.spreadsheetUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                  >
                    🔗 View Spreadsheet
                  </a>
                </div>
              )}
            </div>

            <div className="text-xs text-gray-600 dark:text-gray-400 mt-2">
              Redirecting to dashboard in 3 seconds...
            </div>
          </motion.div>
        )}

        {errorMsg && (
          <motion.p
            key="error"
            initial={{ opacity: 0, y: 8 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -8 }}
            className="text-red-600 dark:text-red-400 font-medium p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800"
          >
            ❌ {errorMsg}
          </motion.p>
        )}
      </AnimatePresence>
    </div>
  );
}
